package main

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"regexp"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

const (
	shortTitleThreshold = 10 // 定义短标题的长度阈值
)

// Task 结构体用于存储任务信息
type Task struct {
	ID          string `json:"id"`
	Title       string `json:"title"`
	IsSubTask   bool   `json:"isSubTask"`
	ParentTitle string `json:"parentTitle"`
	Date        string `json:"date"`      // 新增字段用于存储任务日期
	Status      string `json:"status"`    // 新增字段用于存储任务状态（开始了/完成了）
	TimeSpent   string `json:"timeSpent"` // 新增字段用于存储任务耗时
}

// ZinResponse 结构体用于匹配禅道接口返回的JSON结构
type ZinResponse struct {
	Name     string `json:"name"`
	Selector string `json:"selector"`
	Type     string `json:"type"`
	Data     string `json:"data"`
}

func main() {
	// 加载.env文件
	if err := godotenv.Load(); err != nil {
		log.Fatalf("加载.env文件失败: %v", err)
	}

	router := setupRouter()
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080" // 默认端口
	}
	log.Printf("服务器将在 :%s 端口启动...", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatalf("Gin 服务器启动失败: %v", err)
	}
}

func setupRouter() *gin.Engine {
	router := gin.Default()

	// 提供静态文件服务
	router.Static("/static", "./static")

	// 根路由返回 index.html
	router.GET("/", func(c *gin.Context) {
		c.File("./static/index.html")
	})

	// 新增API路由，用于获取配置信息（如TASK_VIEW_URL）
	router.GET("/api/config", func(c *gin.Context) {
		taskViewURL := os.Getenv("TASK_VIEW_URL")
		if taskViewURL == "" {
			log.Print("WARN: 环境变量 TASK_VIEW_URL 未设置，前端链接可能不完整。")
		}
		c.JSON(http.StatusOK, gin.H{"taskViewURL": taskViewURL})
	})

	// API 路由，用于获取任务数据
	router.POST("/api/tasks", func(c *gin.Context) {
		var requestBody struct {
			Cookie       string            `json:"cookie"`
			ExtraHeaders map[string]string `json:"extraHeaders"`
		}
		if err := c.BindJSON(&requestBody); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求体"})
			return
		}

		if requestBody.Cookie == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Cookie 不能为空"})
			return
		}

		tasks, err := fetchZenTaoTasks(requestBody.Cookie, requestBody.ExtraHeaders)
		if err != nil {
			log.Printf("获取禅道任务失败: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取禅道任务失败", "details": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"tasks": tasks})
	})

	return router
}

// fetchZenTaoTasks 从禅道接口获取并解析任务数据
func fetchZenTaoTasks(cookieHeader string, extraHeaders map[string]string) ([]Task, error) {
	mainURL := os.Getenv("MAIN_URL")
	if mainURL == "" {
		return nil, errors.New("环境变量 MAIN_URL 未设置")
	}

	taskViewURL := os.Getenv("TASK_VIEW_URL")
	if taskViewURL == "" {
		return nil, errors.New("环境变量 TASK_VIEW_URL 未设置")
	}

	log.Printf("正在请求主接口: %s", mainURL)
	client := &http.Client{}

	req, err := http.NewRequest("GET", mainURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置默认请求头
	req.Header.Set("Accept", os.Getenv("ACCEPT"))
	req.Header.Set("Accept-Language", os.Getenv("ACCEPT_LANGUAGE"))
	req.Header.Set("Cache-Control", os.Getenv("CACHE_CONTROL"))
	req.Header.Set("Connection", os.Getenv("CONNECTION"))
	req.Header.Set("Cookie", cookieHeader) // 使用传入的 Cookie
	req.Header.Set("DNT", os.Getenv("DNT"))
	req.Header.Set("Pragma", os.Getenv("PRAGMA"))
	req.Header.Set("Referer", os.Getenv("REFERER"))
	req.Header.Set("User-Agent", os.Getenv("USER_AGENT"))
	req.Header.Set("X-Requested-With", os.Getenv("X_REQUESTED_WITH"))
	req.Header.Set("X-ZIN-App", os.Getenv("X_ZIN_APP"))
	req.Header.Set("X-ZIN-Options", os.Getenv("X_ZIN_OPTIONS"))
	req.Header.Set("X-ZIN-UID", os.Getenv("X_ZIN_UID"))
	req.Header.Set("X-Zin-Cache-Time", os.Getenv("X_ZIN_CACHE_TIME"))

	// 设置额外的请求头
	if extraHeaders != nil {
		for key, value := range extraHeaders {
			if key != "" && value != "" {
				req.Header.Set(key, value)
				log.Printf("DEBUG: 设置额外请求头 %s: %s", key, value)
			}
		}
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求主接口失败: %w", err)
	}
	defer resp.Body.Close()

	log.Printf("主接口响应状态码: %d", resp.StatusCode)
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("主接口请求返回非200状态码: %d", resp.StatusCode)
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取主接口响应体失败: %w", err)
	}

	log.Printf("主接口原始响应体 (前500字符): %s", string(bodyBytes)[:min(len(bodyBytes), 500)])

	// 禅道接口数据是JSON格式，需要先解析JSON
	htmlContent, err := extractHTMLFromJSON(bodyBytes)
	if err != nil {
		return nil, fmt.Errorf("提取HTML内容失败: %w", err)
	}

	log.Printf("提取到的HTML内容 (前500字符): %s", htmlContent[:min(len(htmlContent), 500)])

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		return nil, fmt.Errorf("解析HTML失败: %w", err)
	}

	var tasks []Task

	// 遍历时间线中的所有li元素，这些元素包含日期分组和任务
	doc.Find("ul.timeline > li").Each(func(i int, s *goquery.Selection) {
		// 检查是否是日期分组的li
		dateDiv := s.Find("div.cursor-pointer.leading-5 > span.ml-2")
		if dateDiv.Length() > 0 {
			currentDate := strings.TrimSpace(dateDiv.Text())
			log.Printf("DEBUG: 发现日期分组: %s", currentDate)

			// 在这个日期分组下查找任务
			taskList := s.Find("ul.dynamic.dynamic-tag-left > li")
			if taskList.Length() > 0 {
				taskList.Each(func(j int, taskS *goquery.Selection) {
					taskID := strings.TrimSpace(taskS.Find("span.label").Text())
					taskTitle := strings.TrimSpace(taskS.Find("a").AttrOr("title", ""))

					// 提取任务状态
					statusText := ""
					taskS.Find("span.text-gray").Each(func(k int, statusS *goquery.Selection) {
						text := strings.TrimSpace(statusS.Text())
						if text == "开始了" || text == "完成了" {
							statusText = text
						}
					})

					if taskID != "" && taskTitle != "" {
						task := Task{
							ID:     taskID,
							Title:  taskTitle,
							Date:   currentDate,
							Status: statusText,
						}

						// 判断是否为短标题，如果是，则尝试获取父任务信息
						if len([]rune(taskTitle)) <= shortTitleThreshold {
							parentTitle, isSubTask := getParentTaskTitle(client, taskID, cookieHeader)
							task.IsSubTask = isSubTask
							if isSubTask {
								task.ParentTitle = parentTitle
								task.Title = fmt.Sprintf("%s - %s", parentTitle, taskTitle)
							}
						}

						// 获取任务耗时信息
						timeSpent, err := getTaskTimeSpent(client, taskID, cookieHeader, extraHeaders)
						if err != nil {
							log.Printf("DEBUG: 获取任务 %s 耗时失败: %v", taskID, err)
							task.TimeSpent = "未知"
						} else {
							task.TimeSpent = timeSpent
						}

						tasks = append(tasks, task)
						log.Printf("DEBUG: 提取任务 - ID: %s, Title: %s, Date: %s, Status: %s, TimeSpent: %s",
							task.ID, task.Title, task.Date, task.Status, task.TimeSpent)
					}
				})
			} else {
				log.Printf("DEBUG: 日期分组 %s 下没有找到任务", currentDate)
			}
		}
	})

	return tasks, nil
}

// extractHTMLFromJSON 从JSON响应中提取HTML内容。
func extractHTMLFromJSON(bodyBytes []byte) (string, error) {
	log.Printf("extractHTMLFromJSON 接收到的原始字节流长度: %d", len(bodyBytes))
	log.Printf("extractHTMLFromJSON 接收到的原始字节流 (前500字符): %s", string(bodyBytes[:min(len(bodyBytes), 500)]))

	firstBracketIndex := bytes.IndexByte(bodyBytes, '[')
	if firstBracketIndex == -1 {
		return "", errors.New("未找到JSON起始符 '['")
	}

	lastBracketIndex := bytes.LastIndexByte(bodyBytes, ']')
	if lastBracketIndex == -1 {
		return "", errors.New("未找到JSON结束符 ']'")
	}

	if lastBracketIndex < firstBracketIndex {
		return "", errors.New("JSON起始符 '[' 在结束符 ']' 之后")
	}

	jsonBytes := bodyBytes[firstBracketIndex : lastBracketIndex+1]

	log.Printf("用于JSON解组的字节流长度: %d", len(jsonBytes))
	log.Printf("用于JSON解组的字节流 (前500字符): %s", string(jsonBytes[:min(len(jsonBytes), 500)]))

	var rawResponses []json.RawMessage
	err := json.Unmarshal(jsonBytes, &rawResponses)
	if err != nil {
		return "", fmt.Errorf("原始JSON解组为RawMessage失败: %w", err)
	}

	for _, rawMsg := range rawResponses {
		var resp ZinResponse
		err := json.Unmarshal(rawMsg, &resp)
		if err == nil {
			if resp.Name == "main" && resp.Type == "html" {
				unescapedHTML := resp.Data
				log.Printf("成功提取HTML数据，长度: %d", len(unescapedHTML))
				return unescapedHTML, nil
			}
		}
	}
	return "", errors.New("未找到名为 main 且类型为html 的响应数据")
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// getParentTaskTitle 获取子任务的父任务标题
func getParentTaskTitle(client *http.Client, taskID string, cookieHeader string) (string, bool) {
	detailURL := fmt.Sprintf(os.Getenv("TASK_VIEW_URL"), taskID)
	log.Printf("DEBUG: getParentTaskTitle - 正在请求子任务详情页: %s", detailURL)
	req, err := http.NewRequest("GET", detailURL, nil)
	if err != nil {
		log.Printf("DEBUG: getParentTaskTitle - 创建子任务请求失败: %v", err)
		return "", false
	}
	req.Header.Set("Cookie", cookieHeader) // 使用传入的 Cookie

	resp, err := client.Do(req)
	if err != nil {
		log.Printf("DEBUG: getParentTaskTitle - 请求子任务详情页失败: %v", err)
		return "", false
	}
	defer resp.Body.Close()

	log.Printf("DEBUG: getParentTaskTitle - 子任务详情页响应状态码: %d", resp.StatusCode)
	if resp.StatusCode != http.StatusOK {
		log.Printf("DEBUG: getParentTaskTitle - 子任务详情页请求返回非200状态码: %d", resp.StatusCode)
		return "", false
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("DEBUG: getParentTaskTitle - 读取子任务详情页响应体失败: %v", err)
		return "", false
	}

	log.Printf("DEBUG: getParentTaskTitle - 子任务详情页原始响应体 (前1000字符): %s", string(bodyBytes)[:min(len(bodyBytes), 1000)])

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(bodyBytes))
	if err != nil {
		log.Printf("DEBUG: getParentTaskTitle - 解析子任务详情页HTML失败: %v", err)
		return "", false
	}

	parentTitle := ""
	isSubTask := false

	// 策略1: 从页面标题中提取父任务标题
	pageTitle := doc.Find("title").Text()
	log.Printf("DEBUG: getParentTaskTitle - 页面标题: %s", pageTitle)

	// 预期格式: TASK#<taskID> <subTaskTitle> / <parentTitle> - 禅道
	// 1. 移除 " - 禅道" 后缀
	if strings.HasSuffix(pageTitle, " - 禅道") {
		pageTitle = strings.TrimSuffix(pageTitle, " - 禅道")
	}

	// 2. 找到最后一个 " / " 分隔符，其后面的部分通常是父任务标题或项目名称
	lastSlashIndex := strings.LastIndex(pageTitle, " / ")
	if lastSlashIndex != -1 {
		// 提取 " / " 之后的内容作为候选父任务标题
		candidateParentTitle := strings.TrimSpace(pageTitle[lastSlashIndex+3:])

		// 检查候选标题是否包含当前任务ID（防止误识别）
		if !strings.Contains(candidateParentTitle, taskID) && candidateParentTitle != "" && len([]rune(candidateParentTitle)) > 0 {
			parentTitle = candidateParentTitle
			isSubTask = true
			log.Printf("DEBUG: getParentTaskTitle - 从页面标题中提取到父任务标题: %s", parentTitle)
			return parentTitle, isSubTask
		}
	}

	// 现有策略（如果标题解析失败或不够具体）
	// 策略2: 尝试从面包屑导航中查找父任务标题
	// 禅道的面包屑导航通常是 #pageToolbar .breadcrumb
	doc.Find("#pageToolbar .breadcrumb a[href*='/task-view-']").Each(func(i int, s *goquery.Selection) {
		href, exists := s.Attr("href")
		text := strings.TrimSpace(s.Text())
		// 确保链接是任务详情页，且不是当前任务ID，且文本不为空
		if exists && strings.Contains(href, "/task-view-") && !strings.Contains(href, taskID) && text != "" {
			// 如果文本是"父任务"，尝试获取其兄弟元素（下一个a标签）的文本作为实际标题
			if text == "父任务" {
				nextSibling := s.NextFiltered("a[href*='/task-view-']")
				if nextSibling.Length() > 0 {
					nextText := strings.TrimSpace(nextSibling.Text())
					if nextText != "" && !strings.Contains(nextText, "查看") && !strings.Contains(nextText, "更多") {
						log.Printf("DEBUG: getParentTaskTitle - 在面包屑中找到父任务标签，并提取到父任务标题: %s (链接: %s)", nextText, nextSibling.AttrOr("href", ""))
						parentTitle = nextText
						isSubTask = true
						return // 找到就返回
					}
				}
			} else if !strings.Contains(text, "查看") && !strings.Contains(text, "更多") {
				// 如果文本不是"父任务"，但也不是通用查看/更多，则直接作为父任务标题
				log.Printf("DEBUG: getParentTaskTitle - 在面包屑中找到可能的父任务链接: %s (文本: %s)", href, text)
				parentTitle = text
				isSubTask = true
				return // 找到就返回
			}
		}
	})

	if !isSubTask {
		// 策略3: 尝试从页面内容中查找明确的父任务链接或字段
		// 寻找在 '.detail' 或 '.panel-body' 中直接显示父任务标题的链接
		// 这种链接通常是直接的任务名称，而不是通用标签
		doc.Find(".main-row .detail a[href*='/task-view-'], .panel-body a[href*='/task-view-']").Each(func(i int, s *goquery.Selection) {
			href, exists := s.Attr("href")
			text := strings.TrimSpace(s.Text())
			if exists && strings.Contains(href, "/task-view-") && !strings.Contains(href, taskID) && text != "" {
				// 排除一些不可能是父任务标题的通用文本
				if !strings.Contains(text, "查看") && !strings.Contains(text, "更多") && !strings.Contains(text, "父任务") {
					log.Printf("DEBUG: getParentTaskTitle - 在页面内容中找到可能的父任务链接（通用选择器）：%s (文本: %s)", href, text)
					parentTitle = text
					isSubTask = true
					return // 找到就返回
				}
			}
		})
	}

	if !isSubTask {
		// 策略4: 寻找带有"父任务"文本的 strong 或 div，然后获取其兄弟链接的标题
		doc.Find("strong:contains('父任务'), div:contains('父任务')").Each(func(i int, s *goquery.Selection) {
			if s.Text() == "父任务" {
				// 尝试获取其下一个兄弟链接的文本作为父任务标题
				nextLink := s.NextFiltered("a[href*='/task-view-']")
				if nextLink.Length() > 0 {
					linkText := strings.TrimSpace(nextLink.Text())
					if linkText != "" && !strings.Contains(linkText, "查看") && !strings.Contains(linkText, "更多") {
						log.Printf("DEBUG: getParentTaskTitle - 通过'父任务'标签找到父任务标题: %s (链接: %s)", linkText, nextLink.AttrOr("href", ""))
						parentTitle = linkText
						isSubTask = true
						return // 找到就返回
					}
				}
			}
		})
	}

	// 最终检查，如果还是没有找到，但标题确实很短，可以考虑更深入的查找或者标记为未知父任务
	if !isSubTask && len([]rune(parentTitle)) <= shortTitleThreshold { // 修正了这里的判断条件
		log.Printf("DEBUG: getParentTaskTitle - 任务 %s 标题短，但未能找到明确的父任务。此任务标题为: %s", taskID, parentTitle)
	}

	log.Printf("DEBUG: getParentTaskTitle - 最终结果 - parentTitle: \"%s\", isSubTask: %t", parentTitle, isSubTask)
	return parentTitle, isSubTask
}

// getTaskTimeSpent 获取任务的耗时信息
func getTaskTimeSpent(client *http.Client, taskID string, cookieHeader string, extraHeaders map[string]string) (string, error) {
	detailURL := fmt.Sprintf(os.Getenv("TASK_VIEW_URL"), taskID)
	log.Printf("DEBUG: getTaskTimeSpent - 正在请求任务详情页: %s", detailURL)

	req, err := http.NewRequest("GET", detailURL, nil)
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置Cookie
	req.Header.Set("Cookie", cookieHeader)

	// 设置额外的请求头
	for key, value := range extraHeaders {
		if key != "" && value != "" {
			req.Header.Set(key, value)
		}
	}

	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求任务详情页失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("任务详情页请求返回非200状态码: %d", resp.StatusCode)
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取任务详情页响应体失败: %w", err)
	}

	// 解析HTML以查找工时信息
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(bodyBytes))
	if err != nil {
		return "", fmt.Errorf("解析任务详情页HTML失败: %w", err)
	}

	// 查找工时相关信息
	timeSpent := "0小时"

	// 打印页面内容以便调试
	log.Printf("DEBUG: getTaskTimeSpent - 页面内容 (前2000字符): %s", string(bodyBytes)[:min(len(bodyBytes), 2000)])

	// 策略1: 查找表格中的工时信息 (禅道通常使用表格布局)
	doc.Find("table tr").Each(func(i int, s *goquery.Selection) {
		// 查找包含"工时"、"耗时"、"已消耗"等关键词的行
		rowText := strings.TrimSpace(s.Text())
		if strings.Contains(rowText, "工时") || strings.Contains(rowText, "耗时") || strings.Contains(rowText, "已消耗") || strings.Contains(rowText, "预计") {
			log.Printf("DEBUG: getTaskTimeSpent - 找到可能的工时行: %s", rowText)

			// 在这一行中查找数字+单位的模式
			re := regexp.MustCompile(`(\d+(?:\.\d+)?)\s*(小时|天|h|H|hour|hours)`)
			matches := re.FindStringSubmatch(rowText)
			if len(matches) > 2 {
				unit := matches[2]
				if unit == "h" || unit == "H" || unit == "hour" || unit == "hours" {
					unit = "小时"
				}
				timeSpent = matches[1] + unit
				log.Printf("DEBUG: getTaskTimeSpent - 从表格行提取到工时: %s", timeSpent)
				return
			}
		}
	})

	// 策略2: 查找特定的工时字段标签和值
	doc.Find("td, th, div, span, label").Each(func(i int, s *goquery.Selection) {
		text := strings.TrimSpace(s.Text())

		// 检查是否是工时相关的标签
		if text == "工时" || text == "耗时" || text == "已消耗" || text == "预计工时" || text == "实际工时" {
			log.Printf("DEBUG: getTaskTimeSpent - 找到工时标签: %s", text)

			// 策略2.1: 查找同一行的下一个单元格
			parent := s.Parent()
			if parent.Length() > 0 {
				nextTd := parent.Next()
				if nextTd.Length() > 0 {
					value := strings.TrimSpace(nextTd.Text())
					if value != "" && value != text {
						// 验证是否是有效的工时格式（包含数字和时间单位，排除状态词）
						if isValidTimeFormat(value) {
							timeSpent = value
							log.Printf("DEBUG: getTaskTimeSpent - 通过下一个单元格找到工时: %s", timeSpent)
							return
						}
					}
				}
			}

			// 策略2.2: 查找相邻的元素
			next := s.Next()
			if next.Length() > 0 {
				value := strings.TrimSpace(next.Text())
				if value != "" && value != text {
					if isValidTimeFormat(value) {
						timeSpent = value
						log.Printf("DEBUG: getTaskTimeSpent - 通过相邻元素找到工时: %s", timeSpent)
						return
					}
				}
			}
		}
	})

	// 策略3: 使用更宽泛的正则表达式在整个页面中搜索
	pageText := doc.Text()
	patterns := []string{
		`工时[：:]\s*(\d+(?:\.\d+)?)\s*(小时|天|h|H)`,
		`耗时[：:]\s*(\d+(?:\.\d+)?)\s*(小时|天|h|H)`,
		`已消耗[：:]\s*(\d+(?:\.\d+)?)\s*(小时|天|h|H)`,
		`预计工时[：:]\s*(\d+(?:\.\d+)?)\s*(小时|天|h|H)`,
		`实际工时[：:]\s*(\d+(?:\.\d+)?)\s*(小时|天|h|H)`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(pageText)
		if len(matches) > 2 {
			unit := matches[2]
			if unit == "h" || unit == "H" {
				unit = "小时"
			}
			timeSpent = matches[1] + unit
			log.Printf("DEBUG: getTaskTimeSpent - 通过正则表达式找到工时: %s (模式: %s)", timeSpent, pattern)
			break
		}
	}

	log.Printf("DEBUG: getTaskTimeSpent - 任务 %s 最终工时: %s", taskID, timeSpent)
	return timeSpent, nil
}

// isValidTimeFormat 检查字符串是否是有效的工时格式
func isValidTimeFormat(value string) bool {
	// 排除明显的状态词
	statusWords := []string{"激活", "暂停", "进行中", "已完成", "待处理", "开始", "结束", "关闭", "打开"}
	for _, word := range statusWords {
		if strings.Contains(value, word) {
			return false
		}
	}

	// 检查是否包含数字和时间单位
	timePatterns := []string{
		`\d+(?:\.\d+)?\s*(小时|天|h|H|hour|hours|分钟|min|mins|秒|sec|secs)`,
		`\d+(?:\.\d+)?$`, // 纯数字也认为是有效的（可能是小时数）
	}

	for _, pattern := range timePatterns {
		re := regexp.MustCompile(pattern)
		if re.MatchString(value) {
			return true
		}
	}

	return false
}
