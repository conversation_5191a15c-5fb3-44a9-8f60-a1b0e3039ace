package main

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

const (
	shortTitleThreshold = 10 // 定义短标题的长度阈值
)

// Task 结构体用于存储任务信息
type Task struct {
	ID          string `json:"id"`
	Title       string `json:"title"`
	IsSubTask   bool   `json:"isSubTask"`
	ParentTitle string `json:"parentTitle"`
	Date        string `json:"date"` // 新增字段用于存储任务日期
}

// ZinResponse 结构体用于匹配禅道接口返回的JSON结构
type ZinResponse struct {
	Name     string `json:"name"`
	Selector string `json:"selector"`
	Type     string `json:"type"`
	Data     string `json:"data"`
}

func main() {
	// 加载.env文件
	if err := godotenv.Load(); err != nil {
		log.Fatalf("加载.env文件失败: %v", err)
	}

	router := setupRouter()
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080" // 默认端口
	}
	log.Printf("服务器将在 :%s 端口启动...", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatalf("Gin 服务器启动失败: %v", err)
	}
}

func setupRouter() *gin.Engine {
	router := gin.Default()

	// 提供静态文件服务
	router.Static("/static", "./static")

	// 根路由返回 index.html
	router.GET("/", func(c *gin.Context) {
		c.File("./static/index.html")
	})

	// 新增API路由，用于获取配置信息（如TASK_VIEW_URL）
	router.GET("/api/config", func(c *gin.Context) {
		taskViewURL := os.Getenv("TASK_VIEW_URL")
		if taskViewURL == "" {
			log.Print("WARN: 环境变量 TASK_VIEW_URL 未设置，前端链接可能不完整。")
		}
		c.JSON(http.StatusOK, gin.H{"taskViewURL": taskViewURL})
	})

	// API 路由，用于获取任务数据
	router.POST("/api/tasks", func(c *gin.Context) {
		var requestBody struct {
			Cookie string `json:"cookie"`
		}
		if err := c.BindJSON(&requestBody); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求体"})
			return
		}

		if requestBody.Cookie == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Cookie 不能为空"})
			return
		}

		tasks, err := fetchZenTaoTasks(requestBody.Cookie)
		if err != nil {
			log.Printf("获取禅道任务失败: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "获取禅道任务失败", "details": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"tasks": tasks})
	})

	return router
}

// fetchZenTaoTasks 从禅道接口获取并解析任务数据
func fetchZenTaoTasks(cookieHeader string) ([]Task, error) {
	mainURL := os.Getenv("MAIN_URL")
	if mainURL == "" {
		return nil, errors.New("环境变量 MAIN_URL 未设置")
	}

	taskViewURL := os.Getenv("TASK_VIEW_URL")
	if taskViewURL == "" {
		return nil, errors.New("环境变量 TASK_VIEW_URL 未设置")
	}

	log.Printf("正在请求主接口: %s", mainURL)
	client := &http.Client{}

	req, err := http.NewRequest("GET", mainURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("Accept", os.Getenv("ACCEPT"))
	req.Header.Set("Accept-Language", os.Getenv("ACCEPT_LANGUAGE"))
	req.Header.Set("Cache-Control", os.Getenv("CACHE_CONTROL"))
	req.Header.Set("Connection", os.Getenv("CONNECTION"))
	req.Header.Set("Cookie", cookieHeader) // 使用传入的 Cookie
	req.Header.Set("DNT", os.Getenv("DNT"))
	req.Header.Set("Pragma", os.Getenv("PRAGMA"))
	req.Header.Set("Referer", os.Getenv("REFERER"))
	req.Header.Set("User-Agent", os.Getenv("USER_AGENT"))
	req.Header.Set("X-Requested-With", os.Getenv("X_REQUESTED_WITH"))
	req.Header.Set("X-ZIN-App", os.Getenv("X_ZIN_APP"))
	req.Header.Set("X-ZIN-Options", os.Getenv("X_ZIN_OPTIONS"))
	req.Header.Set("X-ZIN-UID", os.Getenv("X_ZIN_UID"))
	req.Header.Set("X-Zin-Cache-Time", os.Getenv("X_ZIN_CACHE_TIME"))

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求主接口失败: %w", err)
	}
	defer resp.Body.Close()

	log.Printf("主接口响应状态码: %d", resp.StatusCode)
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("主接口请求返回非200状态码: %d", resp.StatusCode)
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取主接口响应体失败: %w", err)
	}

	log.Printf("主接口原始响应体 (前500字符): %s", string(bodyBytes)[:min(len(bodyBytes), 500)])

	// 禅道接口数据是JSON格式，需要先解析JSON
	htmlContent, err := extractHTMLFromJSON(bodyBytes)
	if err != nil {
		return nil, fmt.Errorf("提取HTML内容失败: %w", err)
	}

	log.Printf("提取到的HTML内容 (前500字符): %s", htmlContent[:min(len(htmlContent), 500)])

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		return nil, fmt.Errorf("解析HTML失败: %w", err)
	}

	var tasks []Task
	currentDate := "" // 用于存储当前日期分组

	// 遍历所有的li元素，这些元素可能是日期分组或者任务本身
	doc.Find("ul.dynamic > li").Each(func(i int, s *goquery.Selection) {
		// 检查是否是日期分组的div
		dateDiv := s.Find("div.cursor-pointer.leading-5 > span.ml-2")
		if dateDiv.Length() > 0 {
			currentDate = strings.TrimSpace(dateDiv.Text())
			log.Printf("DEBUG: 发现日期分组: %s", currentDate)
			return // 这是日期分组，跳过处理任务
		}

		// 检查是否是任务li，通过查找任务ID的span.label
		taskIDSelection := s.Find("span.label")
		if taskIDSelection.Length() == 0 {
			// 如果没有找到任务ID，尝试检查是否是包含任务列表的div（如 `border-l` div内的 `ul`）
			// 这通常发生在日期分组下的 `div` 包含 `ul.dynamic.dynamic-tag-left` 时
			innerTaskList := s.Find("ul.dynamic.dynamic-tag-left > li")
			if innerTaskList.Length() > 0 {
				innerTaskList.Each(func(j int, innerS *goquery.Selection) {
					taskID := innerS.Find("span.label").Text()
					taskTitle := innerS.Find("a").AttrOr("title", "")

					if taskID != "" && taskTitle != "" {
						task := Task{
							ID:    taskID,
							Title: taskTitle,
							Date:  currentDate, // 赋予当前日期
						}

						// 判断是否为短标题，如果是，则尝试获取父任务信息
						if len([]rune(taskTitle)) <= shortTitleThreshold {
							parentTitle, isSubTask := getParentTaskTitle(client, taskID, cookieHeader)
							task.IsSubTask = isSubTask
							if isSubTask {
								task.ParentTitle = parentTitle
								task.Title = fmt.Sprintf("%s - %s", parentTitle, taskTitle)
							}
						}
						tasks = append(tasks, task)
						log.Printf("DEBUG: 提取任务 - ID: %s, Title: %s, Date: %s", task.ID, task.Title, task.Date)
					}
				})
			}
			return // 不是直接的任务li，而是包含任务列表的div，已处理其内部任务
		}

		// 如果是直接的任务li（没有包含在日期分组的ul中），处理它
		taskID := taskIDSelection.Text()
		taskTitle := s.Find("a").AttrOr("title", "")

		if taskID != "" && taskTitle != "" {
			task := Task{
				ID:    taskID,
				Title: taskTitle,
				Date:  currentDate, // 赋予当前日期
			}

			// 判断是否为短标题，如果是，则尝试获取父任务信息
			if len([]rune(taskTitle)) <= shortTitleThreshold {
				parentTitle, isSubTask := getParentTaskTitle(client, taskID, cookieHeader)
				task.IsSubTask = isSubTask
				if isSubTask {
					task.ParentTitle = parentTitle
					task.Title = fmt.Sprintf("%s - %s", parentTitle, taskTitle)
				}
			}
			tasks = append(tasks, task)
			log.Printf("DEBUG: 提取任务 - ID: %s, Title: %s, Date: %s", task.ID, task.Title, task.Date)
		}
	})

	return tasks, nil
}

// extractHTMLFromJSON 从JSON响应中提取HTML内容。
func extractHTMLFromJSON(bodyBytes []byte) (string, error) {
	log.Printf("extractHTMLFromJSON 接收到的原始字节流长度: %d", len(bodyBytes))
	log.Printf("extractHTMLFromJSON 接收到的原始字节流 (前500字符): %s", string(bodyBytes[:min(len(bodyBytes), 500)]))

	firstBracketIndex := bytes.IndexByte(bodyBytes, '[')
	if firstBracketIndex == -1 {
		return "", errors.New("未找到JSON起始符 '['")
	}

	lastBracketIndex := bytes.LastIndexByte(bodyBytes, ']')
	if lastBracketIndex == -1 {
		return "", errors.New("未找到JSON结束符 ']'")
	}

	if lastBracketIndex < firstBracketIndex {
		return "", errors.New("JSON起始符 '[' 在结束符 ']' 之后")
	}

	jsonBytes := bodyBytes[firstBracketIndex : lastBracketIndex+1]

	log.Printf("用于JSON解组的字节流长度: %d", len(jsonBytes))
	log.Printf("用于JSON解组的字节流 (前500字符): %s", string(jsonBytes[:min(len(jsonBytes), 500)]))

	var rawResponses []json.RawMessage
	err := json.Unmarshal(jsonBytes, &rawResponses)
	if err != nil {
		return "", fmt.Errorf("原始JSON解组为RawMessage失败: %w", err)
	}

	for _, rawMsg := range rawResponses {
		var resp ZinResponse
		err := json.Unmarshal(rawMsg, &resp)
		if err == nil {
			if resp.Name == "main" && resp.Type == "html" {
				unescapedHTML := resp.Data
				log.Printf("成功提取HTML数据，长度: %d", len(unescapedHTML))
				return unescapedHTML, nil
			}
		}
	}
	return "", errors.New("未找到名为 main 且类型为html 的响应数据")
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// getParentTaskTitle 获取子任务的父任务标题
func getParentTaskTitle(client *http.Client, taskID string, cookieHeader string) (string, bool) {
	detailURL := fmt.Sprintf(os.Getenv("TASK_VIEW_URL"), taskID)
	log.Printf("DEBUG: getParentTaskTitle - 正在请求子任务详情页: %s", detailURL)
	req, err := http.NewRequest("GET", detailURL, nil)
	if err != nil {
		log.Printf("DEBUG: getParentTaskTitle - 创建子任务请求失败: %v", err)
		return "", false
	}
	req.Header.Set("Cookie", cookieHeader) // 使用传入的 Cookie

	resp, err := client.Do(req)
	if err != nil {
		log.Printf("DEBUG: getParentTaskTitle - 请求子任务详情页失败: %v", err)
		return "", false
	}
	defer resp.Body.Close()

	log.Printf("DEBUG: getParentTaskTitle - 子任务详情页响应状态码: %d", resp.StatusCode)
	if resp.StatusCode != http.StatusOK {
		log.Printf("DEBUG: getParentTaskTitle - 子任务详情页请求返回非200状态码: %d", resp.StatusCode)
		return "", false
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("DEBUG: getParentTaskTitle - 读取子任务详情页响应体失败: %v", err)
		return "", false
	}

	log.Printf("DEBUG: getParentTaskTitle - 子任务详情页原始响应体 (前1000字符): %s", string(bodyBytes)[:min(len(bodyBytes), 1000)])

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(bodyBytes))
	if err != nil {
		log.Printf("DEBUG: getParentTaskTitle - 解析子任务详情页HTML失败: %v", err)
		return "", false
	}

	parentTitle := ""
	isSubTask := false

	// 策略1: 从页面标题中提取父任务标题
	pageTitle := doc.Find("title").Text()
	log.Printf("DEBUG: getParentTaskTitle - 页面标题: %s", pageTitle)

	// 预期格式: TASK#<taskID> <subTaskTitle> / <parentTitle> - 禅道
	// 1. 移除 " - 禅道" 后缀
	if strings.HasSuffix(pageTitle, " - 禅道") {
		pageTitle = strings.TrimSuffix(pageTitle, " - 禅道")
	}

	// 2. 找到最后一个 " / " 分隔符，其后面的部分通常是父任务标题或项目名称
	lastSlashIndex := strings.LastIndex(pageTitle, " / ")
	if lastSlashIndex != -1 {
		// 提取 " / " 之后的内容作为候选父任务标题
		candidateParentTitle := strings.TrimSpace(pageTitle[lastSlashIndex+3:])

		// 检查候选标题是否包含当前任务ID（防止误识别）
		if !strings.Contains(candidateParentTitle, taskID) && candidateParentTitle != "" && len([]rune(candidateParentTitle)) > 0 {
			parentTitle = candidateParentTitle
			isSubTask = true
			log.Printf("DEBUG: getParentTaskTitle - 从页面标题中提取到父任务标题: %s", parentTitle)
			return parentTitle, isSubTask
		}
	}

	// 现有策略（如果标题解析失败或不够具体）
	// 策略2: 尝试从面包屑导航中查找父任务标题
	// 禅道的面包屑导航通常是 #pageToolbar .breadcrumb
	doc.Find("#pageToolbar .breadcrumb a[href*='/task-view-']").Each(func(i int, s *goquery.Selection) {
		href, exists := s.Attr("href")
		text := strings.TrimSpace(s.Text())
		// 确保链接是任务详情页，且不是当前任务ID，且文本不为空
		if exists && strings.Contains(href, "/task-view-") && !strings.Contains(href, taskID) && text != "" {
			// 如果文本是"父任务"，尝试获取其兄弟元素（下一个a标签）的文本作为实际标题
			if text == "父任务" {
				nextSibling := s.NextFiltered("a[href*='/task-view-']")
				if nextSibling.Length() > 0 {
					nextText := strings.TrimSpace(nextSibling.Text())
					if nextText != "" && !strings.Contains(nextText, "查看") && !strings.Contains(nextText, "更多") {
						log.Printf("DEBUG: getParentTaskTitle - 在面包屑中找到父任务标签，并提取到父任务标题: %s (链接: %s)", nextText, nextSibling.AttrOr("href", ""))
						parentTitle = nextText
						isSubTask = true
						return // 找到就返回
					}
				}
			} else if !strings.Contains(text, "查看") && !strings.Contains(text, "更多") {
				// 如果文本不是"父任务"，但也不是通用查看/更多，则直接作为父任务标题
				log.Printf("DEBUG: getParentTaskTitle - 在面包屑中找到可能的父任务链接: %s (文本: %s)", href, text)
				parentTitle = text
				isSubTask = true
				return // 找到就返回
			}
		}
	})

	if !isSubTask {
		// 策略3: 尝试从页面内容中查找明确的父任务链接或字段
		// 寻找在 '.detail' 或 '.panel-body' 中直接显示父任务标题的链接
		// 这种链接通常是直接的任务名称，而不是通用标签
		doc.Find(".main-row .detail a[href*='/task-view-'], .panel-body a[href*='/task-view-']").Each(func(i int, s *goquery.Selection) {
			href, exists := s.Attr("href")
			text := strings.TrimSpace(s.Text())
			if exists && strings.Contains(href, "/task-view-") && !strings.Contains(href, taskID) && text != "" {
				// 排除一些不可能是父任务标题的通用文本
				if !strings.Contains(text, "查看") && !strings.Contains(text, "更多") && !strings.Contains(text, "父任务") {
					log.Printf("DEBUG: getParentTaskTitle - 在页面内容中找到可能的父任务链接（通用选择器）：%s (文本: %s)", href, text)
					parentTitle = text
					isSubTask = true
					return // 找到就返回
				}
			}
		})
	}

	if !isSubTask {
		// 策略4: 寻找带有"父任务"文本的 strong 或 div，然后获取其兄弟链接的标题
		doc.Find("strong:contains('父任务'), div:contains('父任务')").Each(func(i int, s *goquery.Selection) {
			if s.Text() == "父任务" {
				// 尝试获取其下一个兄弟链接的文本作为父任务标题
				nextLink := s.NextFiltered("a[href*='/task-view-']")
				if nextLink.Length() > 0 {
					linkText := strings.TrimSpace(nextLink.Text())
					if linkText != "" && !strings.Contains(linkText, "查看") && !strings.Contains(linkText, "更多") {
						log.Printf("DEBUG: getParentTaskTitle - 通过'父任务'标签找到父任务标题: %s (链接: %s)", linkText, nextLink.AttrOr("href", ""))
						parentTitle = linkText
						isSubTask = true
						return // 找到就返回
					}
				}
			}
		})
	}

	// 最终检查，如果还是没有找到，但标题确实很短，可以考虑更深入的查找或者标记为未知父任务
	if !isSubTask && len([]rune(parentTitle)) <= shortTitleThreshold { // 修正了这里的判断条件
		log.Printf("DEBUG: getParentTaskTitle - 任务 %s 标题短，但未能找到明确的父任务。此任务标题为: %s", taskID, parentTitle)
	}

	log.Printf("DEBUG: getParentTaskTitle - 最终结果 - parentTitle: \"%s\", isSubTask: %t", parentTitle, isSubTask)
	return parentTitle, isSubTask
}
