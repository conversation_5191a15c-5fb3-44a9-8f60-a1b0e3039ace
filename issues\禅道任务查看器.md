## 任务：禅道任务查看器 Web 页面转换

### 上下文

用户希望将现有的 Go 命令行工具转换为一个 Web 页面应用。该应用应具备以下功能：
1.  **后端：** 使用 Gin 框架提供服务。
2.  **前端：** 简单的 HTML 页面，支持用户输入禅道 Cookie。
3.  **Cookie 缓存：** 网页端能够缓存用户输入的 Cookie，避免下次重复输入。
4.  **任务显示：** 后端获取禅道任务数据后，前端能够动态展示任务列表。

### 计划

1.  **修改 `go.mod`：** 添加 Gin 框架依赖。
2.  **重构 `main.go` 为后端服务：**
    *   引入 Gin 框架。
    *   创建一个 Gin 路由器。
    *   将现有的获取禅道数据和解析逻辑封装到一个独立的函数 `fetchZenTaoTasks(cookie string) ([]Task, error)` 中。
    *   创建一个 `/api/tasks` 的 POST 路由，该路由接收请求体中的 Cookie，调用 `fetchZenTaoTasks` 函数，并将结果以 JSON 格式返回。
    *   创建一个 `/` 路由，用于提供静态的 `index.html` 文件。
    *   确保后端继续从 `.env` 文件加载 `MAIN_URL` 和 `TASK_VIEW_URL`。
3.  **创建 `static/index.html` 文件：**
    *   包含一个简单的 HTML 结构。
    *   一个 `<input type="text" id="cookieInput">` 用于用户输入 Cookie。
    *   一个 `<button id="fetchButton">` 用于触发数据获取。
    *   一个 `<div id="tasksContainer">` 用于动态显示任务列表。
    *   嵌入 JavaScript 代码：
        *   在页面加载时，检查 `localStorage` 中是否存在名为 `zenTaoCookie` 的项，如果存在，则将其值填充到 `cookieInput` 中。
        *   为 `fetchButton` 添加点击事件监听器。
        *   在点击事件中，获取 `cookieInput` 的值。
        *   将获取到的 Cookie 存储到 `localStorage` 中（`localStorage.setItem('zenTaoCookie', cookieValue)`）。
        *   使用 `fetch` API 向 `/api/tasks` 发送 POST 请求，请求体中包含 Cookie。
        *   处理后端返回的 JSON 数据，并将其动态渲染到 `tasksContainer` 中。
        *   实现基本的错误处理和用户反馈（例如，加载状态、错误消息）。 