<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>禅道任务查看器</title>
    <!-- SheetJS CDN for Excel export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 960px;
            margin: 20px auto;
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        h1 {
            color: #31475a;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
            border-bottom: 2px solid #e8e8e8;
            padding-bottom: 15px;
        }
        .input-group {
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
        }
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        .input-group input[type="text"] {
            flex-grow: 1;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 15px;
            box-sizing: border-box;
            width: 100%;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
        }
        .button-group button {
            flex: 1;
            padding: 12px 20px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
        }
        .button-group button:hover {
            background-color: #40a9ff;
            transform: translateY(-1px);
        }
        .button-group button:active {
            transform: translateY(0);
        }

        .message {
            margin-top: 15px;
            padding: 12px;
            border-radius: 6px;
            text-align: center;
            font-size: 15px;
            font-weight: 500;
            display: none; /* Hidden by default */
        }
        .message.success {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .message.error {
            background-color: #fff1f0;
            color: #f5222d;
            border: 1px solid #ffa39e;
        }
        .message.info {
            background-color: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }

        .task-list {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e8e8e8;
        }
        .date-header {
            font-size: 1.2em;
            font-weight: bold;
            color: #31475a;
            margin-top: 25px;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #d9d9d9;
        }
        .task-item {
            background: #fdfdfd;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            margin-bottom: 12px;
            padding: 15px 20px;
            display: flex;
            flex-direction: column; /* Changed to column for better stacking */
            gap: 5px;
            transition: box-shadow 0.2s ease, transform 0.2s ease;
        }
        .task-item:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
            transform: translateY(-2px);
        }
        .task-item .task-id {
            font-weight: bold;
            color: #1890ff;
            font-size: 1.1em;
        }
        .task-item .task-title {
            font-size: 1em;
            color: #333;
            word-break: break-word; /* Allow word breaking */
        }
        .task-item .parent-info {
            font-size: 0.9em;
            color: #888;
            margin-top: 5px;
            background-color: #f0f2f5;
            padding: 3px 8px;
            border-radius: 4px;
            display: inline-block;
            word-break: break-word;
        }
        /* Styles for links to look like text unless hovered */
        .task-id-link,
        .task-title-link {
            color: inherit; /* Inherit color from parent */
            text-decoration: none; /* Remove underline */
        }
        .task-id-link:hover,
        .task-title-link:hover {
            text-decoration: underline; /* Add underline on hover */
            color: #0056b3; /* A different color on hover for visual feedback */
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>禅道任务查看器</h1>

        <div class="input-group">
            <label for="cookieInput">请输入禅道 Cookie:</label>
            <input type="text" id="cookieInput" placeholder="将您的禅道会话Cookie粘贴到这里">
        </div>
        <div class="button-group">
            <button id="fetchButton">获取任务</button>
            <button id="downloadExcelButton">下载 Excel</button>
        </div>

        <div id="message" class="message"></div>

        <div id="tasksContainer" class="task-list">
            <!-- 任务将在这里动态加载 -->
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const cookieInput = document.getElementById('cookieInput');
            const fetchButton = document.getElementById('fetchButton');
            const downloadExcelButton = document.getElementById('downloadExcelButton');
            const tasksContainer = document.getElementById('tasksContainer');
            const messageDiv = document.getElementById('message');

            let currentTasks = []; // Cache for tasks
            let zenTaoTaskViewURL = ''; // Global variable to store TASK_VIEW_URL

            // Fetch TASK_VIEW_URL from backend
            fetch('/api/config')
                .then(response => response.json())
                .then(config => {
                    if (config.taskViewURL) {
                        // Remove any query parameters from the URL
                        try {
                            const url = new URL(config.taskViewURL);
                            zenTaoTaskViewURL = url.origin + url.pathname; 
                        } catch (e) {
                            console.warn('Invalid TASK_VIEW_URL format, using as is:', config.taskViewURL);
                            zenTaoTaskViewURL = config.taskViewURL;
                        }
                        console.log('Fetched TASK_VIEW_URL:', zenTaoTaskViewURL);
                    } else {
                        console.warn('TASK_VIEW_URL not found in backend config.');
                    }
                })
                .catch(error => {
                    console.error('Error fetching config:', error);
                });

            // 1. 从 Local Storage 加载 Cookie
            const savedCookie = localStorage.getItem('zenTaoCookie');
            if (savedCookie) {
                cookieInput.value = savedCookie;
            }

            fetchButton.addEventListener('click', async () => {
                const cookieValue = cookieInput.value.trim();
                if (!cookieValue) {
                    showMessage('Cookie 不能为空！', 'error');
                    return;
                }

                // 2. 将 Cookie 保存到 Local Storage
                localStorage.setItem('zenTaoCookie', cookieValue);

                showMessage('正在获取任务...', 'info');
                tasksContainer.innerHTML = ''; // Clear previous tasks

                try {
                    const response = await fetch('/api/tasks', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ cookie: cookieValue }),
                    });

                    const data = await response.json();
                    console.log('Backend response data:', data); // For debugging

                    if (response.ok) {
                        if (data.tasks && data.tasks.length > 0) {
                            showMessage(`成功获取 ${data.tasks.length} 个任务！`, 'success');

                            // Group tasks by date
                            const tasksByDate = data.tasks.reduce((acc, task) => {
                                const date = task.date || '无日期'; // Handle tasks without a date
                                if (!acc[date]) {
                                    acc[date] = [];
                                }
                                acc[date].push(task);
                                return acc;
                            }, {});

                            // Sort dates (newest first)
                            const sortedDates = Object.keys(tasksByDate).sort((a, b) => {
                                // Simple date string comparison assuming YYYY年MM月DD日 format
                                // For more robust sorting, convert to Date objects
                                return b.localeCompare(a); 
                            });

                            // Render tasks by date group
                            sortedDates.forEach(date => {
                                const dateHeader = document.createElement('h3');
                                dateHeader.className = 'date-header';
                                dateHeader.textContent = date;
                                tasksContainer.appendChild(dateHeader);

                                tasksByDate[date].forEach(task => {
                                    const taskItem = document.createElement('div');
                                    taskItem.className = 'task-item';

                                    let parentInfoHtml = '';
                                    if (task.isSubTask && task.parentTitle) {
                                        parentInfoHtml = `<div class="parent-info">父任务: ${task.parentTitle}</div>`;
                                    }

                                    // Construct task detail link: remove any existing query params from baseUrl
                                    const taskDetailLink = zenTaoTaskViewURL ? 
                                        `${zenTaoTaskViewURL.replace('%s', task.id)}` : 
                                        `#`; // Fallback if URL is not available

                                    taskItem.innerHTML = `
                                        ${parentInfoHtml}
                                        <a href="${taskDetailLink}" target="_blank" class="task-id-link">
                                            <div class="task-id">工单编号: ${task.id}</div>
                                        </a>
                                        <div class="task-title">${task.title}</div> <!-- Task title is not clickable -->
                                    `;
                                    tasksContainer.appendChild(taskItem);
                                });
                            });

                            currentTasks = data.tasks; // Cache the fetched tasks
                        } else {
                            showMessage('没有找到任务，请检查 Cookie 或禅道配置。', 'info');
                            currentTasks = []; // Clear cached tasks if no results
                        }
                    } else {
                        const errorMessage = data.error || '未知错误';
                        showMessage(`获取任务失败: ${errorMessage}`, 'error');
                        console.error('API Error:', data.details || data.error);
                    }
                } catch (error) {
                    showMessage(`请求失败: ${error.message}`, 'error');
                    console.error('Fetch Error:', error);
                }
            });

            downloadExcelButton.addEventListener('click', () => {
                if (currentTasks.length === 0) {
                    showMessage('没有可下载的任务，请先点击“获取任务”！', 'error');
                    return;
                }

                showMessage('正在生成 Excel 文件...', 'info');

                // Prepare data for Excel
                const worksheetData = [
                    ["工单编号", "标题", "是否子任务", "父任务标题", "日期"]
                ];

                currentTasks.forEach(task => {
                    const isSubTaskText = task.isSubTask ? '是' : '否';
                    worksheetData.push([
                        task.id,
                        task.title,
                        isSubTaskText,
                        task.parentTitle || '',
                        task.date || '' // Include date in Excel export
                    ]);
                });

                const ws = XLSX.utils.aoa_to_sheet(worksheetData);
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, "禅道任务");

                // Save the file
                XLSX.writeFile(wb, "禅道任务.xlsx");

                showMessage('Excel 文件已成功下载！', 'success');
            });

            function showMessage(msg, type) {
                messageDiv.textContent = msg;
                messageDiv.className = `message ${type}`;
                messageDiv.style.display = 'block';

                // 自动隐藏消息
                setTimeout(() => {
                    messageDiv.style.display = 'none';
                }, 5000);
            }
        });
    </script>
</body>
</html>